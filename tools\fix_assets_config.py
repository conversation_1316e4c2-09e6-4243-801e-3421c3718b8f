#!/usr/bin/env python3
"""
<PERSON><PERSON>t to fix assets.json configuration file by:
1. Scanning all files under the "assets" folder recursively
2. Fixing incorrect items in assets.json
3. Updating the "type" property based on folder structure
4. Adding missing entries for files not in the config
"""

import os
import json
from pathlib import Path
from typing import Dict, List

def scan_assets_folder(assets_dir: str) -> Dict[str, str]:
    """
    Scan the assets folder and return a mapping of image files to their types.

    Args:
        assets_dir: Path to the assets directory

    Returns:
        Dict mapping image file paths to their types (folder names)
    """
    assets_path = Path(assets_dir).resolve()
    if not assets_path.exists():
        raise FileNotFoundError(f"Assets directory not found: {assets_dir}")

    file_type_map = {}

    # Scan each type folder
    for type_folder in assets_path.iterdir():
        if type_folder.is_dir():
            type_name = type_folder.name

            # Scan all image files in this type folder
            for image_file in type_folder.iterdir():
                if image_file.is_file() and image_file.suffix.lower() in ['.webp', '.png', '.jpg', '.jpeg']:
                    # Create relative path from assets directory
                    relative_path = str(image_file.relative_to(assets_path.parent)).replace('\\', '/')
                    file_type_map[relative_path] = type_name

    return file_type_map

def extract_prompt_from_filename(filename: str) -> str:
    """
    Extract prompt from filename by converting underscores to spaces and removing file extension.
    Also handles the pattern where the last token might be a duplicate of the first token.

    Args:
        filename: The image filename

    Returns:
        Cleaned prompt string
    """
    # Remove file extension
    name_without_ext = Path(filename).stem

    # Convert underscores to spaces
    prompt = name_without_ext.replace('_', ' ')

    # Split into tokens
    tokens = prompt.split()

    # Remove duplicate last token if it matches first token
    if len(tokens) > 1 and tokens[-1] == tokens[0]:
        tokens = tokens[:-1]

    return ' '.join(tokens)

def load_existing_config(config_path: str) -> List[Dict]:
    """Load existing assets.json configuration."""
    if not os.path.exists(config_path):
        return []

    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def normalize_path(path: str) -> str:
    """Normalize path separators to forward slashes."""
    return path.replace('\\', '/')

def main():
    """Main function to fix the assets configuration."""
    assets_dir = "assets"
    config_path = "assets.json"

    print("Scanning assets folder...")
    file_type_map = scan_assets_folder(assets_dir)

    print(f"Found {len(file_type_map)} image files")

    # Load existing configuration
    print("Loading existing configuration...")
    existing_config = load_existing_config(config_path)

    # Create a mapping of normalized paths to existing entries
    existing_entries = {}
    for entry in existing_config:
        normalized_path = normalize_path(entry['image_path'])
        existing_entries[normalized_path] = entry

    # Track which files we've processed
    processed_files = set()
    updated_config = []

    print("Processing existing entries...")

    # Process existing entries
    for entry in existing_config:
        normalized_path = normalize_path(entry['image_path'])

        if normalized_path in file_type_map:
            # File exists, update type if needed
            correct_type = file_type_map[normalized_path]
            if entry['type'] != correct_type:
                print(f"Updating type for {normalized_path}: {entry['type']} -> {correct_type}")
                entry['type'] = correct_type

            # Update image_path to use forward slashes
            entry['image_path'] = normalized_path
            updated_config.append(entry)
            processed_files.add(normalized_path)
        else:
            print(f"Warning: File not found, skipping: {normalized_path}")

    print("Adding missing entries...")

    # Add missing entries
    for file_path, file_type in file_type_map.items():
        if file_path not in processed_files:
            filename = Path(file_path).name
            prompt = extract_prompt_from_filename(filename)

            new_entry = {
                "prompt": prompt,
                "image_path": file_path,
                "display_name": prompt,  # Will be updated later with Chinese translations
                "type": file_type
            }

            updated_config.append(new_entry)
            print(f"Added new entry: {file_path}")

    # Sort by type and then by prompt for better organization
    updated_config.sort(key=lambda x: (x['type'], x['prompt']))

    # Save updated configuration
    print(f"Saving updated configuration with {len(updated_config)} entries...")
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(updated_config, f, ensure_ascii=False, indent=2)

    print("Configuration updated successfully!")

    # Print summary
    print(f"\nSummary:")
    print(f"- Total entries: {len(updated_config)}")

    type_counts = {}
    for entry in updated_config:
        type_name = entry['type']
        type_counts[type_name] = type_counts.get(type_name, 0) + 1

    for type_name, count in sorted(type_counts.items()):
        print(f"- {type_name}: {count} entries")

if __name__ == "__main__":
    main()

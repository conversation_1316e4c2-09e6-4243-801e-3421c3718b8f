#!/usr/bin/env python3
"""
Script to extract action values from action.json and save them to action.txt
"""

import json

def extract_actions():
    """Extract action values from action.json and save to action.txt"""
    try:
        # Read the action.json file
        with open('action.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract all action values (skip empty values)
        actions = []
        for key, value in data.items():
            if value and value.strip():  # Skip empty or whitespace-only values
                actions.append(value)
        
        # Write actions to action.txt (one per line)
        with open('action.txt', 'w', encoding='utf-8') as f:
            for action in actions:
                f.write(action + '\n')
        
        print(f"Successfully extracted {len(actions)} actions to action.txt")
        
    except FileNotFoundError:
        print("Error: action.json file not found")
    except json.JSONDecodeError:
        print("Error: Invalid JSON format in action.json")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    extract_actions()

#!/usr/bin/env python3
"""
Script to extract "prompt" values from assets.json and save them to role.txt
"""

import json

def extract_prompts():
    """Extract prompt values from assets.json and save to role.txt"""
    try:
        # Read the assets.json file
        with open('assets.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Extract all prompt values
        prompts = []
        for item in data:
            if 'prompt' in item:
                prompts.append(item['prompt'])
        
        # Write prompts to role.txt (one per line)
        with open('role.txt', 'w', encoding='utf-8') as f:
            for prompt in prompts:
                f.write(prompt + '\n')
        
        print(f"Successfully extracted {len(prompts)} prompts to role.txt")
        
    except FileNotFoundError:
        print("Error: assets.json file not found")
    except json.JSONDecodeError:
        print("Error: Invalid JSON format in assets.json")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    extract_prompts()

import json
import base64
import os
import io
from PIL import Image
import glob

def extract_images_from_json(json_file, output_dir):
    """
    Extract base64 encoded images from a JSON file and save them to the output directory.
    
    Args:
        json_file (str): Path to the JSON file containing base64 encoded images
        output_dir (str): Directory to save the extracted images
    """
    print(f"Processing {json_file}...")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load JSON data
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Extract file name without extension
    file_prefix = os.path.splitext(os.path.basename(json_file))[0]
    
    # Process each item in the JSON data
    for i, item in enumerate(data):
        for key, value in item.items():
            # Check if the value is a base64 encoded image
            if isinstance(value, str) and value.startswith('data:image/'):
                # Extract image format and base64 data
                format_start = value.find('/') + 1
                format_end = value.find(';', format_start)
                img_format = value[format_start:format_end]
                
                # Extract base64 data
                base64_start = value.find('base64,') + 7
                base64_data = value[base64_start:]
                
                # Decode base64 data
                try:
                    image_data = base64.b64decode(base64_data)
                    
                    # Create a safe filename from the key
                    safe_key = ''.join(c if c.isalnum() or c in ['-', '_'] else '_' for c in key)
                    filename = f"{file_prefix}_{safe_key}_{i}.{img_format}"
                    filepath = os.path.join(output_dir, filename)
                    
                    # Save the image
                    with open(filepath, 'wb') as img_file:
                        img_file.write(image_data)
                    
                    print(f"Saved image: {filepath}")
                except Exception as e:
                    print(f"Error decoding image for key '{key}': {e}")

def main():
    # Find all output_*.json files
    json_files = glob.glob('output_*.json')
    
    # Extract images from each file
    for json_file in json_files:
        extract_images_from_json(json_file, 'assets')
    
    print("Image extraction complete!")

if __name__ == "__main__":
    main()

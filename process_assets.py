#!/usr/bin/env python3
"""
Script to process assets.json file:
1. Remove duplicate last token if it matches the first token in "prompt"
2. Create "display_name" property by joining "prompt" and "chinese_prompt" with comma
"""

import json
import sys
from pathlib import Path

def process_prompt(prompt):
    """
    Process the prompt string to remove duplicate last token if it matches the first token.
    
    Args:
        prompt (str): The original prompt string with comma-separated tokens
        
    Returns:
        str: The processed prompt string
    """
    if not prompt:
        return prompt
    
    # Split by comma and strip whitespace
    tokens = [token.strip() for token in prompt.split(',')]
    
    # If we have at least 2 tokens and the first and last are the same, remove the last
    if len(tokens) >= 2 and tokens[0] == tokens[-1]:
        tokens = tokens[:-1]
    
    # Join back with comma and space
    return ', '.join(tokens)

def create_display_name(prompt, chinese_prompt):
    """
    Create display name by joining prompt and chinese_prompt.
    
    Args:
        prompt (str): The processed prompt string
        chinese_prompt (str): The Chinese prompt string
        
    Returns:
        str: The display name
    """
    if not prompt or not chinese_prompt:
        return f"{prompt}, {chinese_prompt}" if prompt or chinese_prompt else ""
    
    return f"{prompt}, {chinese_prompt}"

def process_assets_file(input_file, output_file=None):
    """
    Process the assets.json file according to the requirements.
    
    Args:
        input_file (str): Path to the input JSON file
        output_file (str): Path to the output JSON file (optional, defaults to input_file)
    """
    if output_file is None:
        output_file = input_file
    
    try:
        # Read the input file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Process each entry
        processed_count = 0
        for entry in data:
            if 'prompt' in entry:
                # Process the prompt to remove duplicate tokens
                original_prompt = entry['prompt']
                processed_prompt = process_prompt(original_prompt)
                entry['prompt'] = processed_prompt
                
                # Create display_name
                chinese_prompt = entry.get('chinese_prompt', '')
                entry['display_name'] = create_display_name(processed_prompt, chinese_prompt)
                
                processed_count += 1
                
                # Print progress for some entries
                if processed_count <= 5:
                    print(f"Entry {processed_count}:")
                    print(f"  Original prompt: {original_prompt}")
                    print(f"  Processed prompt: {processed_prompt}")
                    print(f"  Chinese prompt: {chinese_prompt}")
                    print(f"  Display name: {entry['display_name']}")
                    print()
        
        # Write the processed data back to file
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"Successfully processed {processed_count} entries.")
        print(f"Output written to: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in '{input_file}': {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def main():
    """Main function to run the script."""
    if len(sys.argv) < 2:
        print("Usage: python process_assets.py <input_file> [output_file]")
        print("Example: python process_assets.py assets.json")
        print("Example: python process_assets.py assets.json assets_processed.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Verify input file exists
    if not Path(input_file).exists():
        print(f"Error: Input file '{input_file}' does not exist.")
        sys.exit(1)
    
    process_assets_file(input_file, output_file)

if __name__ == "__main__":
    main()

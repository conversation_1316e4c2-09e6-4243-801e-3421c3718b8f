import json
import os
import glob

def update_json_files():
    """
    Update the output_*.json files to use image file paths instead of base64 strings.
    """
    # Find all output_*.json files
    json_files = glob.glob('output_*.json')

    for json_file in json_files:
        print(f"Processing {json_file}...")

        # Load JSON data
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Extract file name without extension
        file_prefix = os.path.splitext(os.path.basename(json_file))[0]

        # Process each item in the JSON data
        updated = False
        for i, item in enumerate(data):
            for key, value in item.items():
                # Check if the value is a base64 encoded image
                if isinstance(value, str) and value.startswith('data:image/'):
                    # Create a safe filename from the key
                    safe_key = ''.join(c if c.isalnum() or c in ['-', '_'] else '_' for c in key)
                    img_format = "webp"  # Assuming all images are webp format
                    filename = f"{file_prefix}_{safe_key}_{i}.{img_format}"
                    filepath = os.path.join("assets", filename)

                    # Check if the file exists
                    if os.path.exists(filepath):
                        # Update the value to use the file path
                        item[key] = filepath
                        updated = True

        # Save the updated JSON data if changes were made
        if updated:
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"Updated {json_file}")
        else:
            print(f"No changes made to {json_file}")

if __name__ == "__main__":
    update_json_files()

import json
import requests
import time
from typing import List, Dict

# Groq API configuration
GROQ_API_KEY = "********************************************************"  # Get free API key from https://console.groq.com/
GROQ_API_URL = "https://api.groq.com/openai/v1/chat/completions"

def classify_batch_with_groq_agentic(prompts: List[str]) -> List[str]:
    """
    Classify a batch of character prompts using Gro<PERSON>'s agentic tooling with built-in web search.
    Returns a list of classifications in the same order as input prompts.
    """
    print(f"   Using Groq agentic tooling with built-in web search...")

    # Create a prompt that instructs the AI to search for each character online
    system_message = """You are a character classification assistant with web search capabilities. For each character provided, you MUST search online to find accurate information about their age, gender, and appearance before classifying them.

IMPORTANT: Use your web search capabilities to look up each character from official sources, wikis, or character databases to get accurate information.

Classify each character as exactly one of: female_adult, female_kid, male_adult, male_kid, or other.

Classification guidelines:
- female_adult: Adult female characters (18+ years old)
- female_kid: Child/young female characters (under 18)
- male_adult: Adult male characters (18+ years old)
- male_kid: Child/young male characters (under 18)
- other: Non-human characters, ambiguous gender, or unclear age

CRITICAL: You must respond with ONLY a JSON array and nothing else. No explanations, no search process descriptions, no additional text. Just the JSON array.

Example response format:
["female_adult", "male_adult", "female_kid", "other", "male_kid"]"""

    # Format the batch of prompts
    prompt_list = "\n".join([f"{i+1}. {prompt}" for i, prompt in enumerate(prompts)])
    user_message = f"""Search online for information about each of these {len(prompts)} characters and classify them based on accurate information you find:

{prompt_list}

Search for their official age, gender, and appearance information from reliable sources before classifying.

RESPOND WITH ONLY A JSON ARRAY - NO OTHER TEXT:"""

    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }

    # Use agentic model with built-in web search
    model = "compound-beta-mini"

    payload = {
        "model": model,
        "messages": [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ],
        "temperature": 0,
        "max_tokens": 1000
    }

    try:
        response = requests.post(GROQ_API_URL, headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            response_data = response.json()
            result_text = response_data['choices'][0]['message']['content'].strip()

            try:
                # Parse the response as JSON array directly (should be clean now)
                result_text = result_text.strip()
                classifications = json.loads(result_text)

                # Validate it's a list of strings
                if not isinstance(classifications, list) or not all(isinstance(item, str) for item in classifications):
                    raise ValueError("Response is not a valid list of classification strings")

                # Validate we got the right number of classifications
                if len(classifications) != len(prompts):
                    print(f"Warning: Expected {len(prompts)} classifications, got {len(classifications)}")
                    # Pad with 'other' if needed
                    while len(classifications) < len(prompts):
                        classifications.append('other')
                    classifications = classifications[:len(prompts)]

                return classifications

            except (json.JSONDecodeError, ValueError) as e:
                print(f"Error parsing JSON response: {e}")
                print(f"Response was: {result_text}")
                # Fallback: return 'other' for all items in this batch
                return ['other'] * len(prompts)
        else:
            print(f"❌ Groq API error {response.status_code}: {response.text}")
            return ['other'] * len(prompts)

    except Exception as e:
        error_msg = str(e)
        if "rate_limit" in error_msg.lower():
            print(f"⚠️  Rate limit exceeded. Consider increasing delays between batches.")
            print(f"   Falling back to 'other' classification for this batch.")
        elif "timeout" in error_msg.lower():
            print(f"⚠️  Request timeout. Consider increasing timeout or reducing batch size.")
            print(f"   Falling back to 'other' classification for this batch.")
        else:
            print(f"❌ Error calling Groq API: {e}")
            print(f"   Falling back to 'other' classification for this batch.")
        # Fallback: return 'other' for all items in this batch
        return ['other'] * len(prompts)

def process_in_batches(data: List[Dict], batch_size: int = 50) -> List[Dict]:
    """
    Process the data in batches to avoid rate limits and improve efficiency.
    """
    total_items = len(data)
    print(f"Processing {total_items} items in batches of {batch_size}")

    for i in range(0, total_items, batch_size):
        batch_end = min(i + batch_size, total_items)
        batch = data[i:batch_end]

        print(f"Processing batch {i//batch_size + 1}: items {i+1}-{batch_end}")

        # Extract prompts from this batch
        prompts = [item['prompt'] for item in batch]

        # Get classifications for this batch using Groq agentic tooling
        classifications = classify_batch_with_groq_agentic(prompts)

        # Assign classifications back to the items
        for j, classification in enumerate(classifications):
            if i + j < len(data):
                data[i + j]['type'] = classification

        # Add a small delay between batches to be respectful to the API
        # if batch_end < total_items:
        #     print("Waiting 2 seconds before next batch...")
        #     time.sleep(2)

    return data

# Load the data
with open('assets.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"Total items in assets.json: {len(data)}")

# Show which classification method is being used
print("🌐 Using Groq AGENTIC TOOLING with built-in web search for accurate character classification")
print("   This will automatically search online for each character's information before classifying")

# Uncomment the line below to test with only a few items first:
# data = data[:3]  # Only process first 3 items for testing

# Process in batches (smaller batch size for agentic search)
batch_size = 5
data = process_in_batches(data, batch_size=batch_size)

# Save the results
with open('assets_with_type.json', 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=2)

print(f"Completed! Processed {len(data)} items and saved to assets_with_type.json")
#!/usr/bin/env python3
"""
Script to process assets.json file:
1. Remove the "chinese_prompt" property
2. Clean up "display_name" by removing duplicate tokens that match the first token when spaces are replaced with underscores
"""

import json
import sys
import os

def clean_display_name(display_name):
    """
    Clean display_name by removing tokens that are the same as the first token 
    when spaces are replaced with underscores.
    """
    if not display_name:
        return display_name
    
    # Split by comma and strip whitespace
    tokens = [token.strip() for token in display_name.split(',')]
    
    if len(tokens) <= 1:
        return display_name
    
    # Get the first token and convert spaces to underscores for comparison
    first_token = tokens[0]
    first_token_normalized = first_token.replace(' ', '_')
    
    # Filter out duplicate tokens (comparing normalized versions)
    cleaned_tokens = [first_token]  # Always keep the first token
    
    for token in tokens[1:]:
        token_normalized = token.replace(' ', '_')
        if token_normalized != first_token_normalized:
            cleaned_tokens.append(token)
    
    return ', '.join(cleaned_tokens)

def process_assets_file(input_file, output_file=None):
    """
    Process the assets.json file according to requirements.
    """
    if output_file is None:
        output_file = input_file
    
    try:
        # Read the input file
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"Processing {len(data)} entries...")
        
        # Process each entry
        processed_count = 0
        for entry in data:
            # Remove chinese_prompt if it exists
            if 'chinese_prompt' in entry:
                del entry['chinese_prompt']
                processed_count += 1
            
            # Clean up display_name
            if 'display_name' in entry:
                original_display_name = entry['display_name']
                cleaned_display_name = clean_display_name(original_display_name)
                
                if cleaned_display_name != original_display_name:
                    entry['display_name'] = cleaned_display_name
                    print(f"Cleaned: '{original_display_name}' -> '{cleaned_display_name}'")
        
        # Write the processed data back
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"Successfully processed {processed_count} entries.")
        print(f"Output written to: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found.")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in '{input_file}': {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

def main():
    """Main function"""
    if len(sys.argv) < 2:
        print("Usage: python process_assets.py <input_file> [output_file]")
        print("If output_file is not specified, input_file will be overwritten.")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Check if input file exists
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist.")
        sys.exit(1)
    
    process_assets_file(input_file, output_file)

if __name__ == "__main__":
    main()
